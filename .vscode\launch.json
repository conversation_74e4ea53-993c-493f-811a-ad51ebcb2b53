{"version": "0.2.0", "configurations": [{"name": "Debug MCP (dev)", "type": "node", "request": "launch", "runtimeExecutable": "C:\\Users\\<USER>\\node\\node-v20.11.1-win-x64\\npm.cmd", "runtimeArgs": ["run", "mcp:dev"], "console": "integratedTerminal", "preLaunchTask": "build", "env": {"PATH": "C:\\Users\\<USER>\\node\\node-v20.11.1-win-x64;${env:PATH}"}}, {"name": "Debug MCP (prod)", "type": "node", "request": "launch", "runtimeExecutable": "C:\\Users\\<USER>\\node\\node-v20.11.1-win-x64\\npm.cmd", "runtimeArgs": ["run", "mcp:prod"], "console": "integratedTerminal", "preLaunchTask": "build", "env": {"PATH": "C:\\Users\\<USER>\\node\\node-v20.11.1-win-x64;${env:PATH}"}}, {"name": "Profile MCP (dev)", "type": "node", "request": "launch", "runtimeExecutable": "C:\\Users\\<USER>\\node\\node-v20.11.1-win-x64\\npm.cmd", "runtimeArgs": ["run", "profile:dev"], "console": "integratedTerminal", "preLaunchTask": "build", "env": {"PATH": "C:\\Users\\<USER>\\node\\node-v20.11.1-win-x64;${env:PATH}"}}, {"name": "Profile MCP (prod)", "type": "node", "request": "launch", "runtimeExecutable": "C:\\Users\\<USER>\\node\\node-v20.11.1-win-x64\\npm.cmd", "runtimeArgs": ["run", "profile:prod"], "console": "integratedTerminal", "preLaunchTask": "build", "env": {"PATH": "C:\\Users\\<USER>\\node\\node-v20.11.1-win-x64;${env:PATH}"}}]}