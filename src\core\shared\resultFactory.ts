/**
 * Result Factory - Consolidated Object.freeze Result Creation Patterns
 * @notation P:core/shared/resultFactory F:createDetectionResult,createCircuitBreakerResult,createTemplateResult CB:result-creation-consolidated I:existing-types DB:none
 */

import type { 
  AntiPatternDetection, 
  AntiPatternType, 
  AntiPatternReport, 
  CodeFix, 
  FixPlan 
} from '../antiPatterns/detector'
import type { 
  CircuitBreakerInstance, 
  CircuitBreakerMetrics 
} from '../antiPatterns/circuitBreaker'
import type { StandardResult } from './errorHandling'
import type { UnifiedOperationResult } from './unifiedTypes'

/**
 * F:createDetectionResult - Create anti-pattern detection result (CONSOLIDATED PATTERN)
 * @notation P:type,line,column,text,severity,message,fixable F:createDetectionResult CB:none I:AntiPatternDetection DB:none
 */
export const createDetectionResult = (
  type: AntiPatternType,
  line: number,
  column: number,
  text: string,
  severity: 'error' | 'warning' | 'info',
  message: string,
  fixable: boolean
): AntiPatternDetection => {
  return Object.freeze({
    type,
    line,
    column,
    text,
    severity,
    message,
    fixable
  })
}

/**
 * F:createDetectionReport - Create detection report result (CONSOLIDATED PATTERN)
 * @notation P:filePath,detections,detectionsByType,fixableCount,riskAssessment F:createDetectionReport CB:none I:AntiPatternReport DB:none
 */
export const createDetectionReport = (
  filePath: string,
  detections: readonly AntiPatternDetection[],
  detectionsByType: Record<AntiPatternType, number>,
  fixableCount: number,
  riskAssessment: string
): AntiPatternReport => {
  return Object.freeze({
    filePath,
    totalDetections: detections.length,
    detectionsByType: Object.freeze(detectionsByType),
    fixableCount,
    riskAssessment,
    detections: Object.freeze(detections)
  })
}

/**
 * F:createFixResult - Create fix result (CONSOLIDATED PATTERN)
 * @notation P:type,startLine,endLine,originalCode,fixedCode,description F:createFixResult CB:none I:CodeFix DB:none
 */
export const createFixResult = (
  type: AntiPatternType,
  startLine: number,
  endLine: number,
  originalCode: string,
  fixedCode: string,
  description: string
): CodeFix => {
  return Object.freeze({
    type,
    startLine,
    endLine,
    originalCode,
    fixedCode,
    description
  })
}

/**
 * F:createFixPlan - Create fix plan result (CONSOLIDATED PATTERN)
 * @notation P:filePath,detections,fixes,estimatedChanges,riskLevel F:createFixPlan CB:none I:FixPlan DB:none
 */
export const createFixPlan = (
  filePath: string,
  detections: readonly AntiPatternDetection[],
  fixes: readonly CodeFix[],
  estimatedChanges: number,
  riskLevel: 'low' | 'medium' | 'high'
): FixPlan => {
  return Object.freeze({
    filePath,
    detections,
    fixes: Object.freeze(fixes),
    estimatedChanges,
    riskLevel
  })
}

/**
 * F:updateCircuitBreakerInstance - Update circuit breaker instance (CONSOLIDATED PATTERN)
 * @notation P:instance,updates F:updateCircuitBreakerInstance CB:none I:CircuitBreakerInstance DB:none
 */
export const updateCircuitBreakerInstance = (
  instance: CircuitBreakerInstance,
  updates: Partial<Omit<CircuitBreakerInstance, 'name' | 'config'>>
): CircuitBreakerInstance => {
  return Object.freeze({
    ...instance,
    ...updates
  })
}

/**
 * F:createCircuitBreakerMetrics - Create circuit breaker metrics (CONSOLIDATED PATTERN)
 * @notation P:instance F:createCircuitBreakerMetrics CB:none I:CircuitBreakerMetrics DB:none
 */
export const createCircuitBreakerMetrics = (
  instance: CircuitBreakerInstance
): CircuitBreakerMetrics => {
  return Object.freeze({
    state: instance.state,
    failureCount: instance.failureCount,
    successCount: instance.successCount,
    lastFailureTime: instance.lastFailureTime,
    lastSuccessTime: instance.lastSuccessTime,
    totalCalls: instance.totalCalls,
    rejectedCalls: instance.rejectedCalls
  })
}

/**
 * F:createTemplateErrorResult - Create template error result (CONSOLIDATED PATTERN)
 * @notation P:template,vars,engine F:createTemplateErrorResult CB:none I:StandardResult DB:none
 */
export const createTemplateErrorResult = (
  template: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple'
): StandardResult => {
  return Object.freeze({
    success: false,
    data: template,
    aiNotationMetadata: Object.freeze({
      parametersDetected: Object.freeze({}),
      toolsDetected: Object.freeze({}),
      workflowCapable: false,
      validationRequired: false
    }),
    metadata: Object.freeze({
      engine,
      vars: Object.freeze(vars),
      processingTime: 0
    })
  })
}
