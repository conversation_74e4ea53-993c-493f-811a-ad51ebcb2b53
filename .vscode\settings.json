{"terminal.integrated.profiles.windows": {"Git Bash": {"path": "C:\\Program Files\\Git\\bin\\bash.exe", "args": ["--rc<PERSON>le", "${workspaceFolder}/.bashrc"]}, "MCP Dev": {"path": "powershell.exe", "args": ["-NoExit", "-ExecutionPolicy", "Bypass", "-Command", "$env:Path='C:\\Users\\<USER>\\node\\node-v20.11.1-win-x64;' + $env:Path; Set-Location -Path '${workspaceFolder}'; npm run mcp:dev"]}, "MCP Prod": {"path": "powershell.exe", "args": ["-NoExit", "-ExecutionPolicy", "Bypass", "-Command", "$env:Path='C:\\Users\\<USER>\\node\\node-v20.11.1-win-x64;' + $env:Path; Set-Location -Path '${workspaceFolder}'; npm run mcp:prod"]}, "MCP Prod (Compiled)": {"path": "powershell.exe", "args": ["-NoExit", "-ExecutionPolicy", "Bypass", "-Command", "$env:Path='C:\\Users\\<USER>\\node\\node-v20.11.1-win-x64;' + $env:Path; Set-Location -Path '${workspaceFolder}'; npm run mcp:compiled"]}, "Clean": {"path": "powershell.exe", "args": ["-NoExit", "-ExecutionPolicy", "Bypass", "-Command", "$env:Path='C:\\Users\\<USER>\\node\\node-v20.11.1-win-x64;' + $env:Path; Set-Location -Path '${workspaceFolder}'; npm run clean"]}, "Ollama Serve": {"path": "powershell.exe", "args": ["-NoExit", "-ExecutionPolicy", "Bypass", "-Command", "$env:Path='C:\\Users\\<USER>\\node\\node-v20.11.1-win-x64;' + $env:Path; Set-Location -Path '${workspaceFolder}'; npm run ollama:serve"]}}, "terminal.integrated.defaultProfile.windows": "PowerShell", "typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "files.associations": {"*.ps1": "powershell"}}