/**
 * Anti-Pattern Detector - AI-Optimized Pattern Detection
 * @notation P:core/antiPatterns/detector F:detectAntiPatterns,generateFixPlan,applyFixes CB:none I:AntiPatternDetection,FixPlan DB:none
 */

import { Project, SourceFile, SyntaxKind } from 'ts-morph'
import { ANTI_PATTERNS } from '../constants'
import { createDetectionResult, createDetectionReport, createFixResult, createFixPlan } from '../shared/resultFactory'

export type AntiPatternType =
  | 'class'
  | 'defaultExport'
  | 'anyType'
  | 'globalState'
  | 'thisKeyword'
  | 'newKeyword'
  | 'mutation'

export type AntiPatternDetection = {
  readonly type: AntiPatternType
  readonly line: number
  readonly column: number
  readonly text: string
  readonly severity: 'error' | 'warning' | 'info'
  readonly message: string
  readonly fixable: boolean
}

export type FixPlan = {
  readonly filePath: string
  readonly detections: readonly AntiPatternDetection[]
  readonly fixes: readonly CodeFix[]
  readonly estimatedChanges: number
  readonly riskLevel: 'low' | 'medium' | 'high'
}

export type CodeFix = {
  readonly type: AntiPatternType
  readonly startLine: number
  readonly endLine: number
  readonly originalCode: string
  readonly fixedCode: string
  readonly description: string
}

export type AntiPatternReport = {
  readonly filePath: string
  readonly totalDetections: number
  readonly detectionsByType: Record<AntiPatternType, number>
  readonly fixableCount: number
  readonly riskAssessment: string
  readonly detections: readonly AntiPatternDetection[]
}

/**
 * F:detectAntiPatterns - Detect all anti-patterns in source code
 * @notation P:sourceCode,filePath F:detectAntiPatterns CB:none I:AntiPatternReport DB:none
 */
export const detectAntiPatterns = (sourceCode: string, filePath: string): AntiPatternReport => {
  const lines = sourceCode.split('\n')
  const detections: AntiPatternDetection[] = []

  lines.forEach((line, index) => {
    if (ANTI_PATTERNS.classes.test(line)) {
      detections.push(
        Object.freeze({
          type: 'class',
          line: index + 1,
          column: line.search(ANTI_PATTERNS.classes) + 1,
          text: line.trim(),
          severity: 'error',
          message: 'Class detected - should be converted to pure functions',
          fixable: true
        })
      )
    }

    if (ANTI_PATTERNS.defaultExports.test(line)) {
      detections.push(
        Object.freeze({
          type: 'defaultExport',
          line: index + 1,
          column: line.search(ANTI_PATTERNS.defaultExports) + 1,
          text: line.trim(),
          severity: 'error',
          message: 'Default export detected - use named exports only',
          fixable: true
        })
      )
    }

    if (ANTI_PATTERNS.anyTypes.test(line)) {
      detections.push(
        Object.freeze({
          type: 'anyType',
          line: index + 1,
          column: line.search(ANTI_PATTERNS.anyTypes) + 1,
          text: line.trim(),
          severity: 'error',
          message: 'Any type detected - use explicit union types',
          fixable: true
        })
      )
    }

    if (ANTI_PATTERNS.globalState.test(line)) {
      detections.push(
        Object.freeze({
          type: 'globalState',
          line: index + 1,
          column: line.search(ANTI_PATTERNS.globalState) + 1,
          text: line.trim(),
          severity: 'warning',
          message: 'Global state detected - use immutable state management',
          fixable: true
        })
      )
    }

    if (ANTI_PATTERNS.thisKeyword.test(line)) {
      detections.push(
        Object.freeze({
          type: 'thisKeyword',
          line: index + 1,
          column: line.search(ANTI_PATTERNS.thisKeyword) + 1,
          text: line.trim(),
          severity: 'error',
          message: 'This keyword detected - use pure functions',
          fixable: true
        })
      )
    }

    if (ANTI_PATTERNS.newKeyword.test(line)) {
      detections.push(
        Object.freeze({
          type: 'newKeyword',
          line: index + 1,
          column: line.search(ANTI_PATTERNS.newKeyword) + 1,
          text: line.trim(),
          severity: 'warning',
          message: 'New keyword detected - use factory functions',
          fixable: true
        })
      )
    }

    if (ANTI_PATTERNS.mutations.test(line)) {
      detections.push(
        Object.freeze({
          type: 'mutation',
          line: index + 1,
          column: line.search(ANTI_PATTERNS.mutations) + 1,
          text: line.trim(),
          severity: 'warning',
          message: 'Mutation detected - use immutable patterns',
          fixable: true
        })
      )
    }
  })

  const detectionsByType = detections.reduce(
    (acc, detection) => {
      acc[detection.type] = (acc[detection.type] || 0) + 1
      return acc
    },
    {} as Record<AntiPatternType, number>
  )

  const fixableCount = detections.filter(d => d.fixable).length
  const errorCount = detections.filter(d => d.severity === 'error').length

  let riskAssessment = 'low'
  if (errorCount > 10) riskAssessment = 'high'
  else if (errorCount > 5) riskAssessment = 'medium'

  return Object.freeze({
    filePath,
    totalDetections: detections.length,
    detectionsByType: Object.freeze(detectionsByType),
    fixableCount,
    riskAssessment,
    detections: Object.freeze(detections)
  })
}

/**
 * F:generateFixPlan - Generate a plan to fix detected anti-patterns
 * @notation P:report F:generateFixPlan CB:none I:FixPlan DB:none
 */
export const generateFixPlan = (report: AntiPatternReport): FixPlan => {
  const fixes: CodeFix[] = []

  report.detections.forEach(detection => {
    if (!detection.fixable) return

    let fixedCode = ''
    let description = ''

    switch (detection.type) {
      case 'class':
        fixedCode = convertClassToFunctions(detection.text)
        description = 'Convert class to pure functions'
        break
      case 'defaultExport':
        fixedCode = convertToNamedExport(detection.text)
        description = 'Convert default export to named export'
        break
      case 'anyType':
        fixedCode = convertAnyToUnion(detection.text)
        description = 'Convert any type to explicit union'
        break
      case 'globalState':
        fixedCode = convertToImmutableState(detection.text)
        description = 'Convert to immutable state pattern'
        break
      case 'thisKeyword':
        fixedCode = convertThisToParameter(detection.text)
        description = 'Convert this usage to explicit parameter'
        break
      case 'newKeyword':
        fixedCode = convertNewToFactory(detection.text)
        description = 'Convert new keyword to factory function'
        break
      case 'mutation':
        fixedCode = convertToImmutable(detection.text)
        description = 'Convert mutation to immutable operation'
        break
    }

    fixes.push(
      Object.freeze({
        type: detection.type,
        startLine: detection.line,
        endLine: detection.line,
        originalCode: detection.text,
        fixedCode,
        description
      })
    )
  })

  const estimatedChanges = fixes.length
  let riskLevel: 'low' | 'medium' | 'high' = 'low'

  if (estimatedChanges > 20) riskLevel = 'high'
  else if (estimatedChanges > 10) riskLevel = 'medium'

  return Object.freeze({
    filePath: report.filePath,
    detections: report.detections,
    fixes: Object.freeze(fixes),
    estimatedChanges,
    riskLevel
  })
}

/**
 * F:applyFixes - Apply fixes to source code
 * @notation P:sourceCode,fixPlan F:applyFixes CB:none I:string DB:none
 */
export const applyFixes = (sourceCode: string, fixPlan: FixPlan): string => {
  const lines = sourceCode.split('\n')

  const sortedFixes = [...fixPlan.fixes].sort((a, b) => b.startLine - a.startLine)

  sortedFixes.forEach(fix => {
    const lineIndex = fix.startLine - 1
    if (lineIndex >= 0 && lineIndex < lines.length) {
      lines[lineIndex] = fix.fixedCode
    }
  })

  return lines.join('\n')
}

const convertClassToFunctions = (classLine: string): string => {
  const className = classLine.match(/class\s+(\w+)/)?.[1] || 'Unknown'
  return `// TODO: Convert ${className} class to pure functions`
}

const convertToNamedExport = (exportLine: string): string => {
  return exportLine.replace(/export\s+default\s+/, 'export const ')
}

const convertAnyToUnion = (anyLine: string): string => {
  return anyLine.replace(/:\s*any\b/, ': unknown')
}

const convertToImmutableState = (stateLine: string): string => {
  return stateLine.replace(/^(\s*)(let|var)(\s+)/, '$1const$3')
}

const convertThisToParameter = (thisLine: string): string => {
  return thisLine.replace(/\bthis\./g, 'state.')
}

const convertNewToFactory = (newLine: string): string => {
  const match = newLine.match(/new\s+(\w+)/)
  if (match) {
    const className = match[1]
    return newLine.replace(/new\s+\w+/, `create${className}`)
  }
  return newLine
}

const convertToImmutable = (mutationLine: string): string => {
  return `// TODO: Convert mutation to immutable operation: ${mutationLine.trim()}`
}
